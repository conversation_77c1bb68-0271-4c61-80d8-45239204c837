@extends($activeTemplate . 'layouts.master')
@section('content')
<div class="row justify-content-center">
    <div class="col-lg-12">
        <div class="card bg-dark">
            <div class="card-header text-center py-3 position-relative" style="background-color: #BE8400;">
                <h3 class="text-white">@lang('Submit Coin')</h3>
                @if(session()->has('promote_after_submit'))
                <span class="position-absolute top-0 end-0 mt-2 me-2 badge rounded-pill bg-dark promote-badge" style="font-size: 0.7rem; cursor: pointer;">⚡</span>
                <div id="promoteBadgeTooltip" class="custom-tooltip" style="top: 40px; right: 10px; left: auto; transform: none; bottom: auto; display: none;">
                    <div class="tooltip-content">
                        <strong>Promoted!</strong><br>
                        Your token will be featured in the Promoted Coins section.
                    </div>
                    <div class="tooltip-arrow" style="bottom: auto; top: -10px; border-bottom: 10px solid #BE8400; border-top: 0;"></div>
                </div>
                @endif
            </div>
            <div class="card-body">
                <form action="{{ route('user.submit.coin.store', ['step' => $step ?? 1]) }}" method="POST" enctype="multipart/form-data" class="submit-form">
                    @csrf

                    <div class="steps-container">
                        <div class="steps">
                            <div class="step {{ $step == 1 ? 'active' : '' }}">
                                <div class="step-number rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: {{ $step == 1 ? '#BE8400' : '#212529' }};">
                                    <span class="fw-bold">1</span>
                                </div>
                                <span class="ms-2 fw-bold">Project Information</span>
                            </div>
                            <div class="step {{ $step == 2 ? 'active' : '' }}">
                                <div class="step-number rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: {{ $step == 2 ? '#BE8400' : '#212529' }};">
                                    <span class="fw-bold">2</span>
                                </div>
                                <span class="ms-2 fw-bold">Links</span>
                            </div>
                            <div class="step {{ $step == 3 ? 'active' : '' }}">
                                <div class="step-number rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: {{ $step == 3 ? '#BE8400' : '#212529' }};">
                                    <span class="fw-bold">3</span>
                                </div>
                                <span class="ms-2 fw-bold">Listing</span>
                            </div>
                        </div>
                    </div>

                    @if($step == 1)
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label required-label">@lang('Upload logo')</label>
                                    <div class="upload-logo-container text-center">
                                        <img src="{{ asset('assets/images/default.png') }}" alt="logo" class="img-thumbnail preview-image mb-2" style="width: 150px; height: 150px; border-radius: 50%; background-color: #2c2f3e;">
                                        <p class="text-muted small"><span style="color: #dc3545;">*</span> @lang('Required dimensions 512x512px 1MB Max')<br><span style="color: #dc3545;">*</span> @lang('Accepted file types: JPG/JPEG, PNG')</p>
                                        <button type="button" class="btn w-100 upload-btn" style="background-color: #BE8400; color: white;">@lang('Upload')</button>
                                        <input type="file" name="logo" id="logo" class="file-upload" accept="image/*" hidden>
                                    </div>
                                    <div class="mt-3 text-center position-relative">
                                        <div id="promoteTooltip" class="custom-tooltip">
                                            <div class="tooltip-content">
                                                <strong>Benefits of Promoting Your Token:</strong><br>
                                                • Instantly listed on our platform<br>
                                                • Featured in Promoted Coins section<br>
                                                • Higher visibility to potential buyers<br>
                                                • Increased credibility and trust<br>
                                                • More traffic to your token page<br>
                                                • Better chances of attracting votes
                                            </div>
                                            <div class="tooltip-arrow"></div>
                                        </div>
                                        <button type="button" id="promoteTokenBtn" class="btn px-4 py-2" style="background-color: #BE8400; color: white;"
                                            title="Promote your token to get featured in the Promoted Coins section and gain more visibility">
                                            ⚡ @lang('Promote Token')
                                        </button>
                                        <p class="text-muted small mt-2">@lang('You\'ll need to purchase promote credit if you don\'t have. Promoted tokens gets listed instantly to "Promoted Coins" section, without admin verification.')</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">@lang('Blockchain') <i class="las la-check-circle text-success"></i></label>
                                    <select name="blockchain" id="blockchain_select" class="form-select" required>
                                        @foreach($blockchains as $blockchain)
                                            <option value="{{ $blockchain }}">{{ $blockchain }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="row blockchain-explorer-url-row d-none">
                                <div class="col-12 mb-3">
                                    <label class="form-label">@lang('Blockchain Explorer URL')</label>
                                    <input type="url" name="blockchain_explorer_url" id="blockchain_explorer_url" class="form-control" placeholder="https://blockchain.com">
                                    <small class="text-muted">Leave blank to use default explorer. Use (YOUR contract-address) below as placeholder for the contract address.</small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label" id="contract_address_label">@lang('Contract Address') <span id="contract_asterisk" style="color: #dc3545;">*</span></label>
                                    <input type="text" name="contract_address" id="contract_address_input" class="form-control @error('contract_address') is-invalid @enderror" value="{{ old('contract_address') }}">
                                    @error('contract_address')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">@lang('Whitepaper')</label>
                                    <input type="url" name="whitepaper" class="form-control" placeholder="https://example.com/whitepaper.pdf">
                                    <small class="text-muted">@lang('Add a URL to your whitepaper document')</small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label required-label">@lang('Name')</label>
                                    <input type="text" name="name" class="form-control" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label required-label">@lang('Symbol')</label>
                                    <input type="text" name="symbol" class="form-control" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">@lang('Video URL')</label>
                                    <input type="url" name="video_url" class="form-control" placeholder="https://youtube.com/watch?v=... or https://vimeo.com/...">
                                    <small class="text-muted">@lang('Add a YouTube, Vimeo or any video URL to display on your token page')</small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label required-label">@lang('Description')</label>
                                    <textarea name="description" class="form-control" rows="6" placeholder="Describe your Token/NFT here. What is the goal, plans, why is this project unique?" required></textarea>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">@lang('Is this a Presale Project?')</label>
                                    <div class="d-flex gap-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="is_presale" id="presaleYes" value="1">
                                            <label class="form-check-label" for="presaleYes">
                                                @lang('Yes')
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="is_presale" id="presaleNo" value="0" checked>
                                            <label class="form-check-label" for="presaleNo">
                                                @lang('No')
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">@lang('Is this a Fair Launch Project?')</label>
                                    <div class="d-flex gap-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="is_fair_launch" id="fairLaunchYes" value="1">
                                            <label class="form-check-label" for="fairLaunchYes">
                                                @lang('Yes')
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="is_fair_launch" id="fairLaunchNo" value="0" checked>
                                            <label class="form-check-label" for="fairLaunchNo">
                                                @lang('No')
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="presale_fields" class="d-none">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label required-label">@lang('Start Date')</label>
                                        <input type="text" name="presale_start_date" class="form-control datepicker presale-date" placeholder="MM/DD/YYYY" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('End Date')</label>
                                        <input type="text" name="presale_end_date" class="form-control datepicker" placeholder="MM/DD/YYYY">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('Softcap')</label>
                                        <input type="text" name="presale_softcap" class="form-control cap-value-input presale-softcap" placeholder="e.g. 50BNB" data-cap-type="softcap">
                                        <small class="text-muted">@lang('Enter a number followed by a currency symbol (max 4 letters)')</small>
                                        <div class="invalid-feedback softcap-error"></div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('Hardcap')</label>
                                        <input type="text" name="presale_hardcap" class="form-control cap-value-input presale-hardcap" placeholder="e.g. 100BNB" data-cap-type="hardcap">
                                        <small class="text-muted">@lang('Enter a number followed by a currency symbol (max 4 letters)')</small>
                                        <div class="invalid-feedback hardcap-error"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label class="form-label">@lang('Presale (Pinksale, PumpFun, etc.) URL')</label>
                                        <input type="url" name="presale_url" class="form-control" placeholder="https://example.com/presale">
                                    </div>
                                </div>
                            </div>
                            <div id="fair_launch_fields" class="d-none">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label required-label">@lang('Start Date')</label>
                                        <input type="text" name="fair_launch_start_date" class="form-control datepicker fair-launch-date" placeholder="MM/DD/YYYY" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('End Date')</label>
                                        <input type="text" name="fair_launch_end_date" class="form-control datepicker" placeholder="MM/DD/YYYY">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('Softcap')</label>
                                        <input type="text" name="fair_launch_softcap" class="form-control cap-value-input fair-launch-softcap" placeholder="e.g. 50BNB" data-cap-type="softcap">
                                        <small class="text-muted">@lang('Enter a number followed by a currency symbol (max 4 letters)')</small>
                                        <div class="invalid-feedback softcap-error"></div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">@lang('Hardcap')</label>
                                        <input type="text" name="fair_launch_hardcap" class="form-control cap-value-input fair-launch-hardcap" placeholder="e.g. 100BNB" data-cap-type="hardcap">
                                        <small class="text-muted">@lang('Enter a number followed by a currency symbol (max 4 letters)')</small>
                                        <div class="invalid-feedback hardcap-error"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label class="form-label">@lang('Fair Launch (Pinksale, PumpFun, etc.) URL')</label>
                                        <input type="url" name="fair_launch_url" class="form-control" placeholder="https://example.com/fairlaunch">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @elseif($step == 2)
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                @if(isset($coinData['logo']))
                                <img src="{{ asset('assets/images/coin_logos/' . $coinData['logo']) }}" alt="logo" class="img-thumbnail preview-image mb-2" style="width: 150px; height: 150px; border-radius: 50%; background-color: #2c2f3e;">
                                @else
                                <img src="{{ asset('assets/images/default.png') }}" alt="logo" class="img-thumbnail preview-image mb-2" style="width: 150px; height: 150px; border-radius: 50%; background-color: #2c2f3e;">
                                @endif
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Website link')</label>
                                    <input type="url" name="website" class="form-control" placeholder="https://yourwebsite.com">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Telegram link')</label>
                                    <input type="text" name="telegram" class="form-control" placeholder="https://t.me/telegram">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Twitter link')</label>
                                    <input type="text" name="twitter" class="form-control" placeholder= "https://twitter.com/your-twitter">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Discord link')</label>
                                    <input type="text" name="discord" class="form-control" placeholder="https://discord.gg/your-discord">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Facebook link')</label>
                                    <input type="text" name="facebook" class="form-control" placeholder="https://www.facebook.com/your-page">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Reddit link')</label>
                                    <input type="text" name="reddit" class="form-control" placeholder="https://www.reddit.com/r/your-reddit">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">@lang('Linktree.ee')</label>
                                    <input type="text" name="linktree" class="form-control" placeholder="https://linktr.ee/your-page">
                                </div>
                            </div>
                        </div>
                    </div>
                    @elseif($step == 3)
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                @if(isset($coinData['logo']))
                                <img src="{{ asset('assets/images/coin_logos/' . $coinData['logo']) }}" alt="logo" class="img-thumbnail preview-image mb-2" style="width: 150px; height: 150px; border-radius: 50%; background-color: #2c2f3e;">
                                @else
                                <img src="{{ asset('assets/images/default.png') }}" alt="logo" class="img-thumbnail preview-image mb-2" style="width: 150px; height: 150px; border-radius: 50%; background-color: #2c2f3e;">
                                @endif
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-12">
                                    <h4 class="mb-4">@lang('Listing Details')</h4>
                                    <p>@lang('Your listing will be reviewed by our team. After review, your token will be listed on our platform. If you have chosen to promote your token,
                                      it will be listed automatically in the "Promoted Coins" section of our site.')</p>
                                    <div class="alert alert-info">
                                        <i class="las la-info-circle"></i> @lang('Please verify all the information before proceeding.')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @endif

                    <div class="row mt-4 form-buttons">
                        <div class="col-md-6 mb-3 mb-md-0">
                            @if($step > 1)
                            <a href="{{ route('user.submit.coin.form', ['step' => $step - 1]) }}" class="btn btn-dark w-100">@lang('Back')</a>
                            @else
                            <a href="{{ route('user.home') }}" class="btn btn-dark w-100">@lang('Back')</a>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn w-100" style="background-color: #BE8400; color: white;">
                                @if($step < 3)
                                    @lang('Next')
                                @else
                                    @lang('Submit')
                                @endif
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Image Size Error Modal -->
<div class="modal custom--modal fade" id="imageSizeErrorModal" tabindex="-1" aria-labelledby="imageSizeErrorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #BE8400; color: #ffffff;">
                <h5 class="modal-title" id="imageSizeErrorModalLabel">@lang('Image Size Error')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="error-message">@lang('Image must be exactly 512x512 pixels')</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('OK')</button>
            </div>
        </div>
    </div>
</div>

<!-- Promote Token Modal -->
<div class="modal custom--modal fade" id="promoteTokenModal" tabindex="-1" aria-labelledby="promoteTokenModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promoteTokenModalLabel">@lang('Promote Token')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="promoteTokenForm">
                    @csrf
                    <input type="hidden" name="chain_id" id="promote_chain_id" value="">
                    <input type="hidden" name="token_address" id="promote_token_address" value="">
                    <input type="hidden" name="token_name" id="promote_token_name" value="">
                    <input type="hidden" name="token_symbol" id="promote_token_symbol" value="">
                    <input type="hidden" name="from_submit_form" value="1">
                    <input type="hidden" name="pre_form" id="promote_pre_form" value="0">
                    <div class="form-group">
                        <label>@lang('Token')</label>
                        <div class="input-group">
                            <span class="form-control token-name-display"></span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Current Promote Credits')</label>
                        <div class="input-group">
                            <span class="form-control">{{ auth()->user()->promote_credits }}</span>
                        </div>
                    </div>
                    <div class="form-group mt-3">
                        <label>@lang('Promotion Days')</label>
                        <div class="input-group">
                            <input type="number" name="days" class="form-control" min="1" max="100" value="1" required>
                            <span class="input-group-text">Days</span>
                        </div>
                        <small class="text-muted">Each day costs 1 promote credit. The token will be displayed in the Promoted Coins section for the specified number of days.</small>
                    </div>
                    <div class="form-group mt-3 text-end">
                        @if(auth()->user()->promote_credits > 0)
                        <button type="button" id="confirmPromotionBtn" class="btn btn--base w-100">@lang('Confirm Promotion')</button>
                        @else
                        <button type="button" id="buyPromoteCreditsBtn" class="btn btn--base w-100">@lang('Buy Promote Credit')</button>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
<style>
    .steps-container {
        width: 100%;
        margin-bottom: 20px;
    }
    .steps {
        display: flex;
        justify-content: center;
        gap: 50px;
    }
    .step {
        display: flex;
        align-items: center;
    }
    .upload-logo-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    /* Responsive styles for steps */
    @media (max-width: 991px) {
        .steps {
            gap: 30px;
        }
    }

    @media (max-width: 767px) {
        .steps {
            gap: 15px;
            flex-wrap: wrap;
            justify-content: space-around;
        }
        .step {
            margin-bottom: 10px;
        }
    }

    @media (max-width: 575px) {
        .steps {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        .step {
            width: 100%;
        }
    }
    /* Theme styling for form elements */
    .form-control, .form-select {
        background-color: #2C2F3E;
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .form-control:focus, .form-select:focus {
        background-color: #2C2F3E;
        color: #fff;
        border-color: #31D7A9;
        box-shadow: 0 0 0 0.25rem rgba(49, 215, 169, 0.25);
    }
    .form-select option {
        background-color: #2C2F3E;
        color: #fff;
    }
    .form-select option:hover, .form-select option:checked {
        background-color: #31D7A9;
        color: #fff;
    }
    /* Wider scrollbar for blockchain dropdown - cross-browser compatible */
    #blockchain_select {
        /* Firefox */
        scrollbar-width: auto;
        scrollbar-color: rgba(var(--main), 0.7) rgba(255, 255, 255, 0.1);
    }
    /* Chrome, Edge, Safari */
    #blockchain_select::-webkit-scrollbar {
        width: 12px;
    }
    #blockchain_select::-webkit-scrollbar-thumb {
        background-color: rgba(var(--main), 0.7);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px;
    }
    #blockchain_select::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }
    .card {
        background-color: #2C2F3E;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .card-body {
        background-color: #2C2F3E;
    }
    /* Single red asterisk for required fields */
    .required-label::after {
        content: ' *';
        color: #dc3545;
    }

    /* Center the promote token button */
    #promoteTokenBtn {
        margin: 0 auto;
        display: inline-block;
    }

    /* Custom tooltip styling */
    .custom-tooltip {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 300px;
        margin-bottom: 10px;
        padding: 10px 15px;
        color: #fff;
        background-color: #232a35;
        border: 1px solid #BE8400;
        border-radius: 6px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        font-size: 0.9rem;
        line-height: 1.5;
        text-align: left;
        z-index: 1000;
        display: none;
    }

    .tooltip-content {
        margin-bottom: 5px;
    }

    .tooltip-arrow {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid #BE8400;
    }

    /* Make badge interactive */
    .badge[title] {
        cursor: pointer;
    }

    /* Responsive styles for the submit coin card */
    @media (max-width: 1199px) {
        .card-body {
            padding: 20px;
        }

        .custom-tooltip {
            width: 250px;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 991px) {
        /* Adjust form layout for tablets */
        .row .col-md-4,
        .row .col-md-8,
        .row .col-md-6 {
            padding: 0 10px;
        }

        /* Ensure proper spacing between form groups */
        .mb-3 {
            margin-bottom: 15px !important;
        }

        /* Adjust image preview size */
        .preview-image {
            width: 120px !important;
            height: 120px !important;
        }

        /* Make buttons more touch-friendly */
        .btn {
            padding: 8px 16px;
        }

        /* Adjust modal size for better readability */
        .modal-dialog {
            max-width: 90%;
            margin: 1.75rem auto;
        }
    }

    /* Specific tablet device styles */
    @media only screen and (width: 768px) and (height: 1024px),
           only screen and (width: 820px) and (height: 1180px),
           only screen and (width: 912px) and (height: 1368px),
           only screen and (width: 834px) and (height: 1112px),
           only screen and (width: 810px) and (height: 1080px),
           only screen and (width: 1024px) and (height: 1366px) {
        /* iPad and similar tablet devices */
        .card {
            width: 100%;
            margin: 0 auto;
        }

        /* Adjust column layout for better tablet display */
        .row .col-md-4 {
            width: 100%;
            margin-bottom: 20px;
        }

        .row .col-md-8 {
            width: 100%;
        }

        /* Center the image upload section on tablets */
        .upload-logo-container {
            margin: 0 auto;
            max-width: 300px;
        }

        /* Ensure form controls are properly sized */
        .form-control,
        .form-select {
            height: 45px;
            font-size: 15px;
        }

        /* Adjust spacing for better readability */
        .mb-3 {
            margin-bottom: 18px !important;
        }

        /* Make buttons more touch-friendly on tablets */
        .btn {
            padding: 10px 20px;
            font-size: 16px;
        }

        /* Ensure the footer buttons are properly spaced */
        .mt-4 {
            margin-top: 2rem !important;
        }

        /* Adjust tooltip position for tablets */
        .custom-tooltip {
            width: 300px;
        }
    }

    /* Mobile styles */
    @media (max-width: 767px) {
        /* Stack columns on mobile */
        .row .col-md-4,
        .row .col-md-8,
        .row .col-md-6 {
            width: 100%;
            padding: 0 10px;
            margin-bottom: 15px;
        }

        /* Adjust spacing for mobile */
        .mb-3 {
            margin-bottom: 12px !important;
        }

        /* Center the image upload section */
        .upload-logo-container {
            margin: 0 auto;
            max-width: 250px;
        }

        /* Adjust image preview size */
        .preview-image {
            width: 100px !important;
            height: 100px !important;
        }

        /* Make form controls more touch-friendly */
        .form-control,
        .form-select {
            height: 45px;
            font-size: 14px;
        }

        textarea.form-control {
            height: auto;
        }

        /* Improve button layout on mobile */
        .btn {
            padding: 10px;
            width: 100%;
        }

        /* Form buttons specific styling */
        .form-buttons .btn {
            margin-bottom: 0;
        }

        /* Adjust modal for mobile */
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-content {
            padding: 10px;
        }

        .modal-body {
            padding: 15px 10px;
        }

        .modal-header {
            padding: 10px 15px;
        }

        .modal-footer {
            padding: 10px;
            flex-direction: column;
        }

        .modal-footer .btn {
            margin-bottom: 5px;
            width: 100%;
        }

        /* Fix spacing in the footer buttons */
        .mt-4 .col-md-6 {
            margin-bottom: 10px;
        }

        /* Adjust tooltip position and size */
        .custom-tooltip {
            width: 90%;
            max-width: 300px;
            left: 50%;
        }

        /* Adjust radio buttons for better touch */
        .form-check {
            padding-left: 1.8rem;
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            margin-left: -1.8rem;
        }

        /* Ensure datepicker is usable on mobile */
        .datepicker-dropdown {
            width: 280px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
        }
    }

    /* Small mobile devices */
    @media (max-width: 575px) {
        .card-header {
            padding: 15px 10px;
        }

        .card-body {
            padding: 15px 10px;
        }

        /* Further reduce spacing */
        .mb-3 {
            margin-bottom: 10px !important;
        }

        /* Make text more readable on small screens */
        .form-label {
            font-size: 14px;
        }

        .text-muted {
            font-size: 12px;
        }

        /* Adjust form controls for very small screens */
        .form-control,
        .form-select {
            height: 40px;
            font-size: 13px;
            padding: 8px;
        }
    }
</style>
@endpush

@push('script')
<script>
    (function($) {
        "use strict";

        // Check if there's a pending promotion in the session
        @if(session()->has('promote_after_submit'))
        // Add a visual indicator to the promote button to show it's been confirmed
        $('#promoteTokenBtn').addClass('btn-success').removeClass('btn-primary')
            .html('<i class="las la-check-circle"></i> Promotion Confirmed');

        // Badge is now added to the card header instead of step indicator

        // Only show the notification on step 1
        @if($step == 1)
        // Add a notification at the top of the form
        var notification = `<div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Promotion Confirmed!</strong> Your token will be automatically added to the Promoted Coins section after you complete and submit this form. Please continue filling out all required fields.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>`;
        $('.submit-form').prepend(notification);
        @endif
        @endif

        // Custom tooltip functionality for promote button
        $('#promoteTokenBtn').on('mouseenter', function() {
            $('#promoteTooltip').fadeIn(200);
        }).on('mouseleave', function() {
            $('#promoteTooltip').fadeOut(200);
        });

        // Custom tooltip functionality for promote badge
        $('.promote-badge').on('mouseenter', function() {
            $('#promoteBadgeTooltip').fadeIn(200);
        }).on('mouseleave', function() {
            $('#promoteBadgeTooltip').fadeOut(200);
        });

        // Handle confirm promotion button click with AJAX
        $('#confirmPromotionBtn').on('click', function() {
            var formData = $('#promoteTokenForm').serialize();

            // Show loading state
            $(this).prop('disabled', true).html('<i class="las la-spinner fa-spin"></i> Processing...');

            // Send AJAX request
            $.ajax({
                url: '{{ route("user.token.promote.presubmit") }}',
                type: 'POST',
                data: formData,
                success: function(response) {
                    // If we need to redirect to buy credits, do that
                    if (response.redirect && response.redirect.includes('promote.credits')) {
                        window.location.href = response.redirect;
                        return;
                    }

                    // Log the success response
                    console.log('Promotion confirmed successfully:', response);

                    // Otherwise, show success message and close the modal
                    $('#promoteTokenModal').modal('hide');

                    // Create notification message
                    var message = response.message || 'Your token will be automatically added to the Promoted Coins section after you complete and submit this form.';

                    // Only show the notification on step 1
                    if ('{{ $step }}' == '1') {
                        var notification = `<div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>Promotion Confirmed!</strong> ${message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>`;

                        // Add the notification at the top of the form
                        $('.submit-form').prepend(notification);
                    }

                    // Add a visual indicator to the promote button to show it's been confirmed
                    $('#promoteTokenBtn').addClass('btn-success').removeClass('btn-primary')
                        .html('<i class="las la-check-circle"></i> Promotion Confirmed');

                    // Reload the page to ensure the badge in the header is shown
                    // This is better than trying to add it dynamically

                    // Scroll to the top of the form
                    $('html, body').animate({
                        scrollTop: $('.submit-form').offset().top - 100
                    }, 500);

                    // Reset button state
                    $('#confirmPromotionBtn').prop('disabled', false).text('Confirm Promotion');

                    // Reload the page to ensure session data is properly reflected in the UI
                    // This helps ensure the session data is properly set
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                },
                error: function(xhr) {
                    // Reset button state
                    $('#confirmPromotionBtn').prop('disabled', false).text('Confirm Promotion');

                    // Show error message
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        alert(xhr.responseJSON.message);
                    } else {
                        alert('An error occurred. Please try again.');
                    }
                }
            });
        });

        // Handle buy promote credits button click
        $('#buyPromoteCreditsBtn').on('click', function() {
            // Redirect to the buy promote credits page
            window.location.href = '{{ route("user.plans.buy.promote.credits") }}';
        });

        // Preview uploaded image
        $('.upload-btn').on('click', function() {
            $('.file-upload').click();
        });

        $('.file-upload').on('change', function() {
            var file = this.files[0];
            if (file) {
                // Check file type
                var fileType = file.type;
                if (!fileType.match('image.*')) {
                    // Set error message and show the modal
                    $('#imageSizeErrorModal').find('.error-message').text('Please upload an image file');
                    $('#imageSizeErrorModal').modal('show');
                    $(this).val('');
                    return;
                }

                // Check file size (1MB = 1024 * 1024 bytes)
                if (file.size > 1024 * 1024) {
                    // Set error message and show the modal
                    $('#imageSizeErrorModal').find('.error-message').text('File size must be less than 1MB');
                    $('#imageSizeErrorModal').modal('show');
                    $(this).val('');
                    return;
                }

                // Create image object to check dimensions
                var img = new Image();
                img.onload = function() {
                    // Check if image is 512x512px
                    if (img.width !== 512 || img.height !== 512) {
                        // Set error message and show the modal
                        $('#imageSizeErrorModal').find('.error-message').text('Image must be exactly 512x512 pixels');
                        $('#imageSizeErrorModal').modal('show');
                        $('.file-upload').val('');
                        $('.preview-image').attr('src', '{{ asset("assets/images/default.png") }}');
                    } else {
                        // If dimensions are correct, show the preview
                        $('.preview-image').attr('src', img.src);
                    }
                };

                // Read the file
                var reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        // Presale project toggle
        function updateContractField() {
            var isPresale = $('#presaleYes').is(':checked');
            var isFairLaunch = $('#fairLaunchYes').is(':checked');

            // Hide both fields initially
            $('#presale_fields').addClass('d-none');
            $('#fair_launch_fields').addClass('d-none');

            if (isPresale) {
                $('#presale_fields').removeClass('d-none');
                $('#contract_address_input').removeAttr('required');
                $('#contract_asterisk').hide();
                // Ensure Fair Launch is set to No if Presale is Yes
                if (isFairLaunch) {
                    $('#fairLaunchNo').prop('checked', true);
                }
            } else if (isFairLaunch) {
                $('#fair_launch_fields').removeClass('d-none');
                $('#contract_address_input').removeAttr('required');
                $('#contract_asterisk').hide();
                // Ensure Presale is set to No if Fair Launch is Yes
                if (isPresale) {
                    $('#presaleNo').prop('checked', true);
                }
            } else {
                $('#contract_address_input').attr('required', 'required');
                $('#contract_asterisk').show();
            }
        }

        // Function to toggle blockchain explorer URL field
        function toggleBlockchainExplorerField() {
            var selectedBlockchain = $('#blockchain_select').val();
            if (selectedBlockchain === 'Own Blockchain' || selectedBlockchain === 'Other') {
                $('.blockchain-explorer-url-row').removeClass('d-none');
            } else {
                $('.blockchain-explorer-url-row').addClass('d-none');
                $('#blockchain_explorer_url').val(''); // Clear the field when hidden
            }
        }

        // Run on page load
        updateContractField();
        toggleBlockchainExplorerField();

        // Add event listeners
        $('#presaleYes, #presaleNo, #fairLaunchYes, #fairLaunchNo').on('change', updateContractField);
        $('#blockchain_select').on('change', toggleBlockchainExplorerField);

        // Initialize datepicker
        if(typeof $.fn.datepicker !== 'undefined') {
            $('.datepicker').datepicker({
                format: 'mm/dd/yyyy',
                autoclose: true,
                todayHighlight: true
            });
        }

        // Auto-format date inputs
        $('.datepicker').on('keyup', function(e) {
            var val = $(this).val();
            val = val.replace(/\D/g, '');

            if (val.length > 2 && val.length <= 4) {
                val = val.substring(0, 2) + '/' + val.substring(2);
            } else if (val.length > 4) {
                val = val.substring(0, 2) + '/' + val.substring(2, 4) + '/' + val.substring(4, 8);
            }

            $(this).val(val);
        });

        // Handle promote token button click
        $('#promoteTokenBtn').on('click', function() {
            // Instead of trying to get token data now, we'll just show the promotion modal
            // The actual token data will be determined at form submission time

            // Set placeholder values for display purposes only
            var tokenName = "Your token";
            var tokenSymbol = "TOKEN";

            // Log that we're opening the promotion modal
            console.log('Opening promotion modal - token data will be determined at submission');

            // We don't need to determine chain ID or token address here
            // We'll set a special flag to indicate this is a pre-form promotion

            // Set values in the promote modal
            $('#promoteTokenModal').find('.token-name-display').text('Your token (will be determined after form submission)');

            // Set a special flag to indicate this is a pre-form promotion
            $('#promote_pre_form').val('1');

            // Show the modal
            $('#promoteTokenModal').modal('show');
        });

        // Validate cap value (softcap/hardcap)
        function validateCapValue(value, capType) {
            if (!value) return true; // Empty values are allowed

            // Check if the value matches the pattern: number followed by 1-4 letter currency symbol
            var regex = /^(\d+)([A-Za-z]{1,4})$/;
            if (!regex.test(value)) {
                return false;
            }

            return true;
        }

        // Add validation for cap value inputs
        $('.cap-value-input').on('input', function() {
            var value = $(this).val().trim();
            var capType = $(this).data('cap-type');
            var isValid = validateCapValue(value, capType);

            if (!isValid && value) {
                $(this).addClass('is-invalid');
                $('.' + capType + '-error').text('Enter a number followed by a currency symbol of maximum 4 letters (e.g., 50BNB, 100USDT)');
            } else {
                $(this).removeClass('is-invalid');
                $('.' + capType + '-error').text('');
            }
        });

        // Form validation and submission
        $('.submit-form button[type="submit"]').on('click', function(e) {
            e.preventDefault();

            // Get the form
            var form = $(this).closest('form');

            // For step 1, validate the cap values and image
            if ('{{ $step }}' == '1') {
                // Check if this is a presale or fair launch project
                var isPresale = $('#presaleYes').is(':checked');
                var isFairLaunch = $('#fairLaunchYes').is(':checked');

                // Handle the start date fields based on project type
                if (isPresale) {
                    // Create a hidden input for presale_start_date if it doesn't exist
                    if (!$('input[name="presale_start_date"]').length) {
                        form.append('<input type="hidden" name="presale_start_date" value="">');
                    }
                    // Set the value from the visible field
                    var startDate = $('.presale-date').val();
                    $('input[name="presale_start_date"]').val(startDate);
                } else if (isFairLaunch) {
                    // Create a hidden input for presale_start_date if it doesn't exist
                    if (!$('input[name="presale_start_date"]').length) {
                        form.append('<input type="hidden" name="presale_start_date" value="">');
                    }
                    // Set the value from the fair launch date field
                    var startDate = $('.fair-launch-date').val();
                    $('input[name="presale_start_date"]').val(startDate);
                }

                // If it's a presale or fair launch project, validate softcap and hardcap
                if (isPresale || isFairLaunch) {
                    var softcap, hardcap;

                    // Get the appropriate values based on project type
                    if (isPresale) {
                        softcap = $('.presale-softcap').val().trim();
                        hardcap = $('.presale-hardcap').val().trim();
                    } else if (isFairLaunch) {
                        softcap = $('.fair-launch-softcap').val().trim();
                        hardcap = $('.fair-launch-hardcap').val().trim();
                    }

                    var softcapValid = validateCapValue(softcap, 'softcap');
                    var hardcapValid = validateCapValue(hardcap, 'hardcap');

                    // Show validation errors if needed
                    if (softcap && !softcapValid) {
                        if (isPresale) {
                            $('.presale-softcap').addClass('is-invalid');
                        } else if (isFairLaunch) {
                            $('.fair-launch-softcap').addClass('is-invalid');
                        }
                        $('.softcap-error').text('Enter a number followed by a currency symbol of maximum 4 letters (e.g., 50BNB, 100USDT)');
                        return false;
                    }

                    if (hardcap && !hardcapValid) {
                        if (isPresale) {
                            $('.presale-hardcap').addClass('is-invalid');
                        } else if (isFairLaunch) {
                            $('.fair-launch-hardcap').addClass('is-invalid');
                        }
                        $('.hardcap-error').text('Enter a number followed by a currency symbol of maximum 4 letters (e.g., 100BNB, 200USDT)');
                        return false;
                    }

                    // Create hidden inputs for softcap and hardcap
                    if (!$('input[name="softcap"]').length) {
                        form.append('<input type="hidden" name="softcap" value="">');
                    }
                    if (!$('input[name="hardcap"]').length) {
                        form.append('<input type="hidden" name="hardcap" value="">');
                    }

                    // Set the values from the appropriate fields
                    $('input[name="softcap"]').val(softcap);
                    $('input[name="hardcap"]').val(hardcap);
                }

                // Validate the image if it's being uploaded
                var fileInput = $('#logo')[0];
                if (fileInput && fileInput.files.length > 0) {
                    var file = fileInput.files[0];

                    // Check file size (1MB = 1024 * 1024 bytes)
                    if (file.size > 1024 * 1024) {
                        // Set error message and show the modal
                        $('#imageSizeErrorModal').find('.error-message').text('File size must be less than 1MB');
                        $('#imageSizeErrorModal').modal('show');
                        return false;
                    }

                    var img = new Image();

                    img.onload = function() {
                        if (img.width !== 512 || img.height !== 512) {
                            // Set error message and show the modal
                            $('#imageSizeErrorModal').find('.error-message').text('Image must be exactly 512x512 pixels');
                            $('#imageSizeErrorModal').modal('show');
                            return false;
                        } else {
                            // If dimensions are correct, submit the form
                            form[0].submit();
                        }
                    };

                    var reader = new FileReader();
                    reader.onload = function(e) {
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                } else {
                    // If no file is selected, just submit the form
                    form[0].submit();
                }
            } else {
                // For other steps, just submit the form
                form[0].submit();
            }
        });
    })(jQuery);
</script>
@endpush

